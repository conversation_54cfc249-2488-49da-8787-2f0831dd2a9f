"""
Pydantic schemas for alert-related API endpoints.
"""
from pydantic import BaseModel, validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from .stock import StockResponse


class AlertTypeEnum(str, Enum):
    """Alert type enumeration."""
    PRICE_ABOVE = "price_above"
    PRICE_BELOW = "price_below"
    PRICE_CHANGE = "price_change"
    VOLUME_SURGE = "volume_surge"
    RSI_OVERSOLD = "rsi_oversold"
    RSI_OVERBOUGHT = "rsi_overbought"
    MACD_CROSSOVER = "macd_crossover"
    MOVING_AVERAGE_CROSS = "moving_average_cross"
    BOLLINGER_BREAKOUT = "bollinger_breakout"
    SUPPORT_RESISTANCE = "support_resistance"


class AlertPriorityEnum(str, Enum):
    """Alert priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatusEnum(str, Enum):
    """Alert status enumeration."""
    ACTIVE = "active"
    TRIGGERED = "triggered"
    DISMISSED = "dismissed"
    EXPIRED = "expired"
    PAUSED = "paused"


class AlertConditions(BaseModel):
    """Schema for alert conditions."""
    target_price: Optional[float] = Field(None, gt=0, description="Target price for price alerts")
    threshold: Optional[float] = Field(None, description="Threshold value for various alerts")
    percentage: Optional[float] = Field(None, description="Percentage change threshold")
    timeframe: Optional[str] = Field(None, regex="^(1m|5m|15m|1h|1d)$", description="Timeframe for alert")
    volume_multiplier: Optional[float] = Field(None, gt=1.0, description="Volume surge multiplier")
    rsi_level: Optional[float] = Field(None, ge=0, le=100, description="RSI level threshold")
    
    # Technical indicator specific conditions
    ma_period_short: Optional[int] = Field(None, gt=0, le=200, description="Short MA period")
    ma_period_long: Optional[int] = Field(None, gt=0, le=200, description="Long MA period")
    bollinger_period: Optional[int] = Field(None, gt=0, le=100, description="Bollinger bands period")
    bollinger_std: Optional[float] = Field(None, gt=0, description="Bollinger bands standard deviation")
    
    @validator('ma_period_long')
    def long_period_gt_short(cls, v, values):
        if v is not None and 'ma_period_short' in values and values['ma_period_short'] is not None:
            if v <= values['ma_period_short']:
                raise ValueError('Long MA period must be greater than short MA period')
        return v


class AlertBase(BaseModel):
    """Base alert schema with common fields."""
    stock_id: int = Field(..., gt=0, description="Stock ID")
    alert_type: AlertTypeEnum = Field(..., description="Type of alert")
    title: str = Field(..., min_length=1, max_length=200, description="Alert title")
    message: str = Field(..., min_length=1, max_length=1000, description="Alert message")
    conditions: AlertConditions = Field(..., description="Alert conditions")
    priority: AlertPriorityEnum = Field(AlertPriorityEnum.MEDIUM, description="Alert priority")
    email_notification: bool = Field(True, description="Send email notification")
    push_notification: bool = Field(True, description="Send push notification")
    expires_at: Optional[datetime] = Field(None, description="Alert expiration date")
    
    @validator('title', 'message')
    def strip_strings(cls, v):
        return v.strip()
    
    @validator('expires_at')
    def expires_in_future(cls, v):
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v


class AlertCreate(AlertBase):
    """Schema for creating a new alert."""
    pass


class AlertUpdate(BaseModel):
    """Schema for updating alert information."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    message: Optional[str] = Field(None, min_length=1, max_length=1000)
    conditions: Optional[AlertConditions] = None
    priority: Optional[AlertPriorityEnum] = None
    email_notification: Optional[bool] = None
    push_notification: Optional[bool] = None
    expires_at: Optional[datetime] = None
    status: Optional[AlertStatusEnum] = None
    
    @validator('title', 'message')
    def strip_strings(cls, v):
        return v.strip() if v else v
    
    @validator('expires_at')
    def expires_in_future(cls, v):
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v


class AlertResponse(AlertBase):
    """Schema for alert response data."""
    id: int
    user_id: int
    status: AlertStatusEnum
    trigger_price: Optional[float] = None
    trigger_value: Optional[float] = None
    trigger_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    triggered_at: Optional[datetime] = None
    stock: Optional[StockResponse] = None
    
    class Config:
        from_attributes = True


class AlertSummary(BaseModel):
    """Schema for alert summary (without full details)."""
    id: int
    stock_symbol: str
    alert_type: AlertTypeEnum
    title: str
    priority: AlertPriorityEnum
    status: AlertStatusEnum
    created_at: datetime
    triggered_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AlertStats(BaseModel):
    """Schema for alert statistics."""
    total_alerts: int = Field(..., ge=0)
    active_alerts: int = Field(..., ge=0)
    triggered_alerts: int = Field(..., ge=0)
    dismissed_alerts: int = Field(..., ge=0)
    expired_alerts: int = Field(..., ge=0)
    alerts_by_type: Dict[str, int] = Field(..., description="Count of alerts by type")
    alerts_by_priority: Dict[str, int] = Field(..., description="Count of alerts by priority")
    recent_triggers: List[AlertSummary] = Field(..., description="Recently triggered alerts")
    
    class Config:
        schema_extra = {
            "example": {
                "total_alerts": 25,
                "active_alerts": 15,
                "triggered_alerts": 8,
                "dismissed_alerts": 1,
                "expired_alerts": 1,
                "alerts_by_type": {
                    "price_above": 10,
                    "price_below": 5,
                    "volume_surge": 3,
                    "rsi_oversold": 7
                },
                "alerts_by_priority": {
                    "low": 5,
                    "medium": 15,
                    "high": 4,
                    "critical": 1
                },
                "recent_triggers": []
            }
        }


class BulkAlertOperation(BaseModel):
    """Schema for bulk operations on alerts."""
    alert_ids: List[int] = Field(..., min_items=1, description="List of alert IDs")
    action: str = Field(..., regex="^(dismiss|pause|activate|delete)$", description="Action to perform")
    
    @validator('alert_ids')
    def unique_alert_ids(cls, v):
        if len(v) != len(set(v)):
            raise ValueError('Alert IDs must be unique')
        return v


class AlertTemplate(BaseModel):
    """Schema for alert templates."""
    name: str = Field(..., min_length=1, max_length=100, description="Template name")
    description: Optional[str] = Field(None, max_length=500, description="Template description")
    alert_type: AlertTypeEnum = Field(..., description="Type of alert")
    conditions: AlertConditions = Field(..., description="Default conditions")
    priority: AlertPriorityEnum = Field(AlertPriorityEnum.MEDIUM, description="Default priority")
    
    @validator('name')
    def name_not_empty(cls, v):
        return v.strip()


class AlertTemplateResponse(AlertTemplate):
    """Schema for alert template response."""
    id: int
    user_id: int
    usage_count: int = Field(..., ge=0, description="Number of times template was used")
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AlertNotification(BaseModel):
    """Schema for alert notifications."""
    alert_id: int
    stock_symbol: str
    alert_type: AlertTypeEnum
    title: str
    message: str
    priority: AlertPriorityEnum
    trigger_price: Optional[float] = None
    trigger_value: Optional[float] = None
    triggered_at: datetime
    
    class Config:
        schema_extra = {
            "example": {
                "alert_id": 123,
                "stock_symbol": "AAPL",
                "alert_type": "price_above",
                "title": "AAPL Price Alert",
                "message": "AAPL has crossed above $150.00",
                "priority": "high",
                "trigger_price": 150.25,
                "triggered_at": "2024-01-01T10:30:00Z"
            }
        }


class AlertBacktest(BaseModel):
    """Schema for alert backtesting."""
    alert_type: AlertTypeEnum
    conditions: AlertConditions
    stock_symbol: str
    start_date: datetime
    end_date: datetime
    
    @validator('end_date')
    def end_after_start(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class AlertBacktestResult(BaseModel):
    """Schema for alert backtest results."""
    total_triggers: int = Field(..., ge=0)
    successful_triggers: int = Field(..., ge=0)
    false_positives: int = Field(..., ge=0)
    accuracy: float = Field(..., ge=0, le=1, description="Accuracy percentage")
    avg_trigger_time: Optional[float] = Field(None, description="Average time to trigger in hours")
    trigger_details: List[Dict[str, Any]] = Field(..., description="Detailed trigger information")
    
    class Config:
        schema_extra = {
            "example": {
                "total_triggers": 15,
                "successful_triggers": 12,
                "false_positives": 3,
                "accuracy": 0.8,
                "avg_trigger_time": 2.5,
                "trigger_details": []
            }
        }
