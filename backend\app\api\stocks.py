"""
Stock-related API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models.user import User
from ..schemas.stock import (
    StockResponse, StockCreate, StockUpdate, StockHistory,
    StockScreeningCriteria, StockScreeningResult, StockSearchResult
)
from ..services.stock_service import stock_service
from ..core.security import get_current_active_user
from ..core.exceptions import handle_exceptions, StockNotFoundError
from ..utils.logger import log_api_call

router = APIRouter()


@router.get("/", response_model=List[StockResponse])
@handle_exceptions
async def get_stocks(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    exchange: Optional[str] = Query(None, description="Filter by exchange"),
    search: Optional[str] = Query(None, description="Search by symbol or name"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of stocks with optional filtering."""
    log_api_call("/stocks", "GET", user_id=current_user.id)

    stocks = await stock_service.get_stocks(
        db=db,
        skip=skip,
        limit=limit,
        sector=sector,
        exchange=exchange,
        search=search
    )

    return [StockResponse.from_orm(stock) for stock in stocks]


@router.get("/search", response_model=List[StockSearchResult])
@handle_exceptions
async def search_stocks(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    current_user: User = Depends(get_current_active_user)
):
    """Search for stocks by symbol or name."""
    log_api_call("/stocks/search", "GET", user_id=current_user.id)

    results = await stock_service.search_stocks(q, limit)
    return results


@router.get("/{symbol}", response_model=StockResponse)
@handle_exceptions
async def get_stock_detail(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get detailed information for a specific stock."""
    log_api_call(f"/stocks/{symbol}", "GET", user_id=current_user.id)

    stock = await stock_service.get_stock_by_symbol(db, symbol)
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    return StockResponse.from_orm(stock)


@router.get("/{symbol}/history", response_model=StockHistory)
@handle_exceptions
async def get_stock_history(
    symbol: str,
    timeframe: str = Query("1d", regex="^(1m|5m|15m|1h|1d|1w|1M)$", description="Data timeframe"),
    limit: int = Query(100, ge=1, le=1000, description="Number of data points"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get historical price data for a stock."""
    log_api_call(f"/stocks/{symbol}/history", "GET", user_id=current_user.id)

    # Verify stock exists
    stock = await stock_service.get_stock_by_symbol(db, symbol)
    if not stock:
        raise StockNotFoundError(f"Stock with symbol {symbol} not found")

    history = await stock_service.get_stock_history(db, symbol, timeframe, limit)
    if not history:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Historical data not available"
        )

    return history


@router.post("/screen", response_model=StockScreeningResult)
@handle_exceptions
async def screen_stocks(
    criteria: StockScreeningCriteria,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Screen stocks based on criteria."""
    log_api_call("/stocks/screen", "POST", user_id=current_user.id)

    result = await stock_service.screen_stocks(db, criteria)
    return result


@router.post("/", response_model=StockResponse, status_code=status.HTTP_201_CREATED)
@handle_exceptions
async def create_stock(
    stock_data: StockCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new stock (admin only)."""
    log_api_call("/stocks", "POST", user_id=current_user.id)

    # TODO: Add admin role check
    stock = await stock_service.create_stock(db, stock_data)
    return StockResponse.from_orm(stock)


@router.put("/{stock_id}", response_model=StockResponse)
@handle_exceptions
async def update_stock(
    stock_id: int,
    stock_data: StockUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update stock information (admin only)."""
    log_api_call(f"/stocks/{stock_id}", "PUT", user_id=current_user.id)

    # TODO: Add admin role check
    stock = await stock_service.update_stock(db, stock_id, stock_data)
    if not stock:
        raise StockNotFoundError(f"Stock with ID {stock_id} not found")

    return StockResponse.from_orm(stock)
