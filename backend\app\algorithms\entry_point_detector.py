"""
Entry point detection algorithms for identifying optimal stock entry points.
"""
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from .technical_indicators import (
    calculate_rsi, calculate_macd, calculate_bollinger_bands,
    calculate_sma, calculate_ema, calculate_support_resistance
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class EntryPointDetector:
    """Detects optimal entry points for stock trading."""
    
    def __init__(self):
        self.confidence_threshold = 0.7
        self.min_data_points = 50
    
    def detect_entry_points(self, historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Detect entry points based on multiple technical indicators.
        
        Args:
            historical_data: List of historical price data
            
        Returns:
            Dictionary containing entry point analysis
        """
        if len(historical_data) < self.min_data_points:
            return {"error": "Insufficient data for analysis"}
        
        # Convert to pandas DataFrame for easier analysis
        df = pd.DataFrame(historical_data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # Calculate technical indicators
        indicators = self._calculate_indicators(df)
        
        # Detect various entry patterns
        entry_signals = {
            'bullish_divergence': self._detect_bullish_divergence(df, indicators),
            'oversold_bounce': self._detect_oversold_bounce(df, indicators),
            'breakout_entry': self._detect_breakout_entry(df, indicators),
            'golden_cross': self._detect_golden_cross(df, indicators),
            'support_bounce': self._detect_support_bounce(df, indicators),
            'bollinger_squeeze': self._detect_bollinger_squeeze(df, indicators)
        }
        
        # Calculate overall entry score
        entry_score = self._calculate_entry_score(entry_signals)
        
        # Generate entry recommendation
        recommendation = self._generate_recommendation(entry_signals, entry_score)
        
        return {
            'entry_signals': entry_signals,
            'entry_score': entry_score,
            'recommendation': recommendation,
            'current_price': float(df.iloc[-1]['close']),
            'analysis_date': datetime.utcnow().isoformat(),
            'indicators': indicators
        }
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all required technical indicators."""
        closes = df['close'].values
        highs = df['high'].values
        lows = df['low'].values
        volumes = df['volume'].values
        
        return {
            'rsi': calculate_rsi(closes, 14),
            'macd': calculate_macd(closes, 12, 26, 9),
            'bollinger_bands': calculate_bollinger_bands(closes, 20, 2),
            'sma_20': calculate_sma(closes, 20),
            'sma_50': calculate_sma(closes, 50),
            'ema_12': calculate_ema(closes, 12),
            'ema_26': calculate_ema(closes, 26),
            'support_resistance': calculate_support_resistance(highs, lows)
        }
    
    def _detect_bullish_divergence(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect bullish divergence between price and RSI."""
        try:
            rsi = indicators['rsi']
            closes = df['close'].values
            
            if len(rsi) < 20:
                return {'detected': False, 'confidence': 0.0}
            
            # Look for price making lower lows while RSI makes higher lows
            recent_period = 10
            price_recent = closes[-recent_period:]
            rsi_recent = rsi[-recent_period:]
            
            price_trend = np.polyfit(range(len(price_recent)), price_recent, 1)[0]
            rsi_trend = np.polyfit(range(len(rsi_recent)), rsi_recent, 1)[0]
            
            # Bullish divergence: price trending down, RSI trending up
            divergence_detected = price_trend < 0 and rsi_trend > 0 and rsi[-1] < 40
            
            confidence = 0.8 if divergence_detected else 0.0
            
            return {
                'detected': divergence_detected,
                'confidence': confidence,
                'description': 'Price making lower lows while RSI makes higher lows'
            }
            
        except Exception as e:
            logger.error(f"Error detecting bullish divergence: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_oversold_bounce(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect oversold bounce opportunity."""
        try:
            rsi = indicators['rsi']
            
            if len(rsi) < 5:
                return {'detected': False, 'confidence': 0.0}
            
            current_rsi = rsi[-1]
            prev_rsi = rsi[-2] if len(rsi) > 1 else current_rsi
            
            # RSI below 30 and starting to turn up
            oversold_bounce = current_rsi < 35 and current_rsi > prev_rsi and current_rsi > 25
            
            confidence = 0.7 if oversold_bounce else 0.0
            
            return {
                'detected': oversold_bounce,
                'confidence': confidence,
                'current_rsi': current_rsi,
                'description': 'RSI oversold and starting to recover'
            }
            
        except Exception as e:
            logger.error(f"Error detecting oversold bounce: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_breakout_entry(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect breakout above resistance with volume confirmation."""
        try:
            closes = df['close'].values
            volumes = df['volume'].values
            support_resistance = indicators['support_resistance']
            
            if len(closes) < 20:
                return {'detected': False, 'confidence': 0.0}
            
            current_price = closes[-1]
            avg_volume = np.mean(volumes[-20:])
            current_volume = volumes[-1]
            
            # Check if price broke above recent resistance
            resistance_levels = support_resistance.get('resistance', [])
            if not resistance_levels:
                return {'detected': False, 'confidence': 0.0}
            
            nearest_resistance = min(resistance_levels, key=lambda x: abs(x - current_price))
            
            # Breakout conditions
            price_breakout = current_price > nearest_resistance * 1.01  # 1% above resistance
            volume_confirmation = current_volume > avg_volume * 1.5
            
            breakout_detected = price_breakout and volume_confirmation
            confidence = 0.8 if breakout_detected else 0.0
            
            return {
                'detected': breakout_detected,
                'confidence': confidence,
                'resistance_level': nearest_resistance,
                'volume_ratio': current_volume / avg_volume,
                'description': 'Price breakout above resistance with volume confirmation'
            }
            
        except Exception as e:
            logger.error(f"Error detecting breakout entry: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_golden_cross(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect golden cross (50 SMA crossing above 200 SMA)."""
        try:
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']
            
            if len(sma_20) < 2 or len(sma_50) < 2:
                return {'detected': False, 'confidence': 0.0}
            
            # Check if 20 SMA crossed above 50 SMA recently
            current_20 = sma_20[-1]
            current_50 = sma_50[-1]
            prev_20 = sma_20[-2]
            prev_50 = sma_50[-2]
            
            golden_cross = (current_20 > current_50 and prev_20 <= prev_50)
            
            confidence = 0.6 if golden_cross else 0.0
            
            return {
                'detected': golden_cross,
                'confidence': confidence,
                'sma_20': current_20,
                'sma_50': current_50,
                'description': '20 SMA crossed above 50 SMA'
            }
            
        except Exception as e:
            logger.error(f"Error detecting golden cross: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_support_bounce(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect bounce from support level."""
        try:
            closes = df['close'].values
            lows = df['low'].values
            support_resistance = indicators['support_resistance']
            
            if len(closes) < 10:
                return {'detected': False, 'confidence': 0.0}
            
            current_price = closes[-1]
            recent_low = min(lows[-5:])
            
            support_levels = support_resistance.get('support', [])
            if not support_levels:
                return {'detected': False, 'confidence': 0.0}
            
            nearest_support = min(support_levels, key=lambda x: abs(x - recent_low))
            
            # Check if price bounced from support
            touched_support = abs(recent_low - nearest_support) / nearest_support < 0.02  # Within 2%
            bouncing_up = current_price > recent_low * 1.01  # 1% above recent low
            
            support_bounce = touched_support and bouncing_up
            confidence = 0.7 if support_bounce else 0.0
            
            return {
                'detected': support_bounce,
                'confidence': confidence,
                'support_level': nearest_support,
                'recent_low': recent_low,
                'description': 'Price bouncing from support level'
            }
            
        except Exception as e:
            logger.error(f"Error detecting support bounce: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_bollinger_squeeze(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Detect Bollinger Bands squeeze (low volatility before breakout)."""
        try:
            bollinger = indicators['bollinger_bands']
            
            if not bollinger or len(bollinger['upper']) < 20:
                return {'detected': False, 'confidence': 0.0}
            
            upper = bollinger['upper']
            lower = bollinger['lower']
            
            # Calculate band width
            band_widths = [(u - l) / ((u + l) / 2) for u, l in zip(upper, lower)]
            
            if len(band_widths) < 20:
                return {'detected': False, 'confidence': 0.0}
            
            current_width = band_widths[-1]
            avg_width = np.mean(band_widths[-20:])
            
            # Squeeze detected if current width is significantly below average
            squeeze_detected = current_width < avg_width * 0.7
            
            confidence = 0.5 if squeeze_detected else 0.0
            
            return {
                'detected': squeeze_detected,
                'confidence': confidence,
                'current_width': current_width,
                'avg_width': avg_width,
                'description': 'Bollinger Bands squeeze indicating potential breakout'
            }
            
        except Exception as e:
            logger.error(f"Error detecting Bollinger squeeze: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _calculate_entry_score(self, entry_signals: Dict[str, Dict]) -> float:
        """Calculate overall entry score based on all signals."""
        total_score = 0.0
        signal_count = 0
        
        for signal_name, signal_data in entry_signals.items():
            if signal_data.get('detected', False):
                confidence = signal_data.get('confidence', 0.0)
                total_score += confidence
                signal_count += 1
        
        # Normalize score
        if signal_count > 0:
            return min(total_score / len(entry_signals), 1.0)
        
        return 0.0
    
    def _generate_recommendation(self, entry_signals: Dict, entry_score: float) -> Dict[str, Any]:
        """Generate entry recommendation based on analysis."""
        if entry_score >= 0.7:
            action = "STRONG_BUY"
            reason = "Multiple strong entry signals detected"
        elif entry_score >= 0.5:
            action = "BUY"
            reason = "Good entry signals present"
        elif entry_score >= 0.3:
            action = "WATCH"
            reason = "Some positive signals, monitor closely"
        else:
            action = "HOLD"
            reason = "No strong entry signals detected"
        
        # Count active signals
        active_signals = [name for name, data in entry_signals.items() if data.get('detected', False)]
        
        return {
            'action': action,
            'reason': reason,
            'entry_score': entry_score,
            'active_signals': active_signals,
            'signal_count': len(active_signals)
        }


def detect_entry_point(historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Convenience function to detect entry points.
    
    Args:
        historical_data: List of historical price data
        
    Returns:
        Entry point analysis
    """
    detector = EntryPointDetector()
    return detector.detect_entry_points(historical_data)
