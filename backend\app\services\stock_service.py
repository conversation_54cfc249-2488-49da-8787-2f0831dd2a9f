"""
Stock service for business logic related to stock operations.
"""
import asyncio
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta

from ..models.stock import Stock, StockPriceHistory
from ..schemas.stock import (
    StockCreate, StockUpdate, StockResponse, StockScreeningCriteria,
    StockScreeningResult, StockSearchResult, StockHistory
)
from ..services.market_data_service import market_data_service
from ..algorithms.technical_indicators import calculate_all_indicators
from ..utils.logger import get_logger, log_screening_result
from ..core.exceptions import StockNotFoundError, ValidationError

logger = get_logger(__name__)


class StockService:
    """Service class for stock-related operations."""
    
    def __init__(self):
        self.market_data_service = market_data_service
    
    async def get_stock_by_symbol(self, db: Session, symbol: str) -> Optional[Stock]:
        """Get stock by symbol."""
        try:
            return db.query(Stock).filter(
                Stock.symbol == symbol.upper(),
                Stock.is_active == True
            ).first()
        except Exception as e:
            logger.error(f"Error getting stock by symbol {symbol}: {e}")
            return None
    
    async def get_stock_by_id(self, db: Session, stock_id: int) -> Optional[Stock]:
        """Get stock by ID."""
        try:
            return db.query(Stock).filter(
                Stock.id == stock_id,
                Stock.is_active == True
            ).first()
        except Exception as e:
            logger.error(f"Error getting stock by ID {stock_id}: {e}")
            return None
    
    async def create_stock(self, db: Session, stock_data: StockCreate) -> Stock:
        """Create a new stock."""
        try:
            # Check if stock already exists
            existing_stock = await self.get_stock_by_symbol(db, stock_data.symbol)
            if existing_stock:
                raise ValidationError(f"Stock with symbol {stock_data.symbol} already exists")
            
            # Create new stock
            stock = Stock(
                symbol=stock_data.symbol.upper(),
                name=stock_data.name,
                exchange=stock_data.exchange,
                sector=stock_data.sector,
                industry=stock_data.industry,
                market_cap=stock_data.market_cap,
                shares_outstanding=stock_data.shares_outstanding
            )
            
            db.add(stock)
            db.commit()
            db.refresh(stock)
            
            logger.info(f"Created new stock: {stock.symbol}")
            return stock
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating stock: {e}")
            raise
    
    async def update_stock(self, db: Session, stock_id: int, stock_data: StockUpdate) -> Optional[Stock]:
        """Update stock information."""
        try:
            stock = await self.get_stock_by_id(db, stock_id)
            if not stock:
                raise StockNotFoundError(f"Stock with ID {stock_id} not found")
            
            # Update fields
            update_data = stock_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(stock, field, value)
            
            stock.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(stock)
            
            logger.info(f"Updated stock: {stock.symbol}")
            return stock
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating stock {stock_id}: {e}")
            raise
    
    async def get_stocks(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        sector: Optional[str] = None,
        exchange: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Stock]:
        """Get list of stocks with filtering."""
        try:
            query = db.query(Stock).filter(Stock.is_active == True)
            
            if sector:
                query = query.filter(Stock.sector == sector)
            
            if exchange:
                query = query.filter(Stock.exchange == exchange)
            
            if search:
                search_term = f"%{search}%"
                query = query.filter(
                    or_(
                        Stock.symbol.ilike(search_term),
                        Stock.name.ilike(search_term)
                    )
                )
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting stocks: {e}")
            return []
    
    async def update_stock_price_data(self, db: Session, stock: Stock) -> bool:
        """Update stock with latest price data."""
        try:
            quote_data = await self.market_data_service.get_stock_quote(stock.symbol)
            if not quote_data:
                return False
            
            # Update stock with latest data
            stock.current_price = quote_data.get('current_price')
            stock.previous_close = quote_data.get('previous_close')
            stock.open_price = quote_data.get('open_price')
            stock.day_high = quote_data.get('day_high')
            stock.day_low = quote_data.get('day_low')
            stock.volume = quote_data.get('volume')
            stock.price_change = quote_data.get('price_change')
            stock.price_change_percent = quote_data.get('price_change_percent')
            stock.last_data_update = datetime.utcnow()
            
            # Calculate technical indicators if we have price data
            if stock.current_price:
                try:
                    # Get historical data for technical indicators
                    historical_data = await self.get_stock_history(
                        db, stock.symbol, "1d", 200
                    )
                    if historical_data and len(historical_data.data) > 20:
                        prices = [point.close for point in historical_data.data]
                        volumes = [point.volume for point in historical_data.data]
                        
                        indicators = calculate_all_indicators(prices, volumes)
                        stock.technical_indicators = indicators
                        
                except Exception as e:
                    logger.warning(f"Error calculating technical indicators for {stock.symbol}: {e}")
            
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error updating stock price data for {stock.symbol}: {e}")
            db.rollback()
            return False
    
    async def get_stock_history(
        self,
        db: Session,
        symbol: str,
        timeframe: str = "1d",
        limit: int = 100
    ) -> Optional[StockHistory]:
        """Get historical price data for a stock."""
        try:
            # First try to get from database
            stock = await self.get_stock_by_symbol(db, symbol)
            if not stock:
                return None
            
            # Get historical data from market data service
            historical_data = await self.market_data_service.get_historical_data(
                symbol, timeframe, limit
            )
            
            if historical_data:
                return StockHistory(
                    symbol=symbol,
                    timeframe=timeframe,
                    data=historical_data
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting stock history for {symbol}: {e}")
            return None
    
    async def screen_stocks(
        self,
        db: Session,
        criteria: StockScreeningCriteria
    ) -> StockScreeningResult:
        """Screen stocks based on criteria."""
        start_time = datetime.utcnow()
        
        try:
            query = db.query(Stock).filter(
                Stock.is_active == True,
                Stock.is_tradable == True
            )
            
            # Apply price filters
            if criteria.min_price is not None:
                query = query.filter(Stock.current_price >= criteria.min_price)
            if criteria.max_price is not None:
                query = query.filter(Stock.current_price <= criteria.max_price)
            
            # Apply volume filters
            if criteria.min_volume is not None:
                query = query.filter(Stock.volume >= criteria.min_volume)
            
            # Apply market cap filters
            if criteria.min_market_cap is not None:
                query = query.filter(Stock.market_cap >= criteria.min_market_cap)
            if criteria.max_market_cap is not None:
                query = query.filter(Stock.market_cap <= criteria.max_market_cap)
            
            # Apply sector filter
            if criteria.sectors:
                query = query.filter(Stock.sector.in_(criteria.sectors))
            
            # Apply exchange filter
            if criteria.exchanges:
                query = query.filter(Stock.exchange.in_(criteria.exchanges))
            
            # Apply technical indicator filters
            if criteria.rsi_min is not None or criteria.rsi_max is not None:
                if criteria.rsi_min is not None:
                    query = query.filter(
                        func.json_extract(Stock.technical_indicators, '$.rsi') >= criteria.rsi_min
                    )
                if criteria.rsi_max is not None:
                    query = query.filter(
                        func.json_extract(Stock.technical_indicators, '$.rsi') <= criteria.rsi_max
                    )
            
            # Apply volume surge filter
            if criteria.volume_surge_threshold is not None:
                query = query.filter(
                    Stock.volume >= Stock.avg_volume * criteria.volume_surge_threshold
                )
            
            # Apply price change filters
            if criteria.price_change_min is not None:
                query = query.filter(Stock.price_change_percent >= criteria.price_change_min)
            if criteria.price_change_max is not None:
                query = query.filter(Stock.price_change_percent <= criteria.price_change_max)
            
            # Execute query
            results = query.limit(1000).all()  # Limit to prevent excessive results
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Log screening result
            log_screening_result(
                criteria.dict(),
                len(results),
                execution_time
            )
            
            return StockScreeningResult(
                criteria=criteria,
                results=[StockResponse.from_orm(stock) for stock in results],
                total_matches=len(results),
                execution_time=execution_time,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error screening stocks: {e}")
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            return StockScreeningResult(
                criteria=criteria,
                results=[],
                total_matches=0,
                execution_time=execution_time,
                timestamp=datetime.utcnow()
            )
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[StockSearchResult]:
        """Search for stocks by symbol or name."""
        try:
            # Use market data service for search
            search_results = await self.market_data_service.search_stocks(query, limit)
            
            return [
                StockSearchResult(
                    symbol=result['symbol'],
                    name=result['name'],
                    exchange=result['exchange'],
                    type=result['type'],
                    currency=result.get('currency', 'USD')
                )
                for result in search_results
            ]
            
        except Exception as e:
            logger.error(f"Error searching stocks for query '{query}': {e}")
            return []


# Global service instance
stock_service = StockService()
