"""
Stock screening algorithms for filtering stocks based on various criteria.
"""
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
import pandas as pd
import numpy as np

from ..models.stock import Stock
from ..schemas.stock import StockScreeningCriteria
from .technical_indicators import calculate_rsi, calculate_macd, calculate_bollinger_bands
from ..utils.logger import get_logger

logger = get_logger(__name__)


class StockScreener:
    """Main stock screening class with various filtering algorithms."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def screen_stocks(self, criteria: StockScreeningCriteria) -> List[Stock]:
        """
        Screen stocks based on provided criteria.
        
        Args:
            criteria: Screening criteria including price, volume, technical indicators
            
        Returns:
            List of stocks that match the criteria
        """
        logger.info(f"Starting stock screening with criteria: {criteria}")
        
        # Start with base query
        query = self.db.query(Stock).filter(Stock.is_active == True)
        
        # Apply basic filters
        query = self._apply_basic_filters(query, criteria)
        
        # Apply technical filters
        query = self._apply_technical_filters(query, criteria)
        
        # Apply sector/industry filters
        query = self._apply_sector_filters(query, criteria)
        
        # Execute query and get results
        stocks = query.limit(criteria.limit or 100).all()
        
        # Apply advanced technical analysis filters
        filtered_stocks = self._apply_advanced_filters(stocks, criteria)
        
        logger.info(f"Screening completed. Found {len(filtered_stocks)} stocks")
        return filtered_stocks
    
    def _apply_basic_filters(self, query, criteria: StockScreeningCriteria):
        """Apply basic price and volume filters."""
        
        # Price filters
        if criteria.min_price is not None:
            query = query.filter(Stock.current_price >= criteria.min_price)
        
        if criteria.max_price is not None:
            query = query.filter(Stock.current_price <= criteria.max_price)
        
        # Volume filters
        if criteria.min_volume is not None:
            query = query.filter(Stock.volume >= criteria.min_volume)
        
        if criteria.max_volume is not None:
            query = query.filter(Stock.volume <= criteria.max_volume)
        
        # Market cap filters
        if criteria.min_market_cap is not None:
            query = query.filter(Stock.market_cap >= criteria.min_market_cap)
        
        if criteria.max_market_cap is not None:
            query = query.filter(Stock.market_cap <= criteria.max_market_cap)
        
        # Price change filters
        if criteria.min_price_change is not None:
            query = query.filter(Stock.price_change_percent >= criteria.min_price_change)
        
        if criteria.max_price_change is not None:
            query = query.filter(Stock.price_change_percent <= criteria.max_price_change)
        
        return query
    
    def _apply_technical_filters(self, query, criteria: StockScreeningCriteria):
        """Apply technical indicator filters using database stored indicators."""
        
        # RSI filters
        if criteria.rsi_min is not None or criteria.rsi_max is not None:
            if criteria.rsi_min is not None:
                query = query.filter(
                    func.json_extract_path_text(Stock.technical_indicators, 'indicators', 'rsi', '-1').cast(float) >= criteria.rsi_min
                )
            if criteria.rsi_max is not None:
                query = query.filter(
                    func.json_extract_path_text(Stock.technical_indicators, 'indicators', 'rsi', '-1').cast(float) <= criteria.rsi_max
                )
        
        return query
    
    def _apply_sector_filters(self, query, criteria: StockScreeningCriteria):
        """Apply sector and industry filters."""
        
        if criteria.sectors:
            query = query.filter(Stock.sector.in_(criteria.sectors))
        
        if criteria.industries:
            query = query.filter(Stock.industry.in_(criteria.industries))
        
        return query
    
    def _apply_advanced_filters(self, stocks: List[Stock], criteria: StockScreeningCriteria) -> List[Stock]:
        """Apply advanced technical analysis filters that require computation."""
        
        filtered_stocks = []
        
        for stock in stocks:
            try:
                # Skip if no technical indicators
                if not stock.technical_indicators:
                    continue
                
                indicators = stock.technical_indicators.get('indicators', {})
                
                # Apply RSI filters if not already applied in database
                if not self._check_rsi_criteria(indicators, criteria):
                    continue
                
                # Apply MACD filters
                if not self._check_macd_criteria(indicators, criteria):
                    continue
                
                # Apply Bollinger Bands filters
                if not self._check_bollinger_criteria(indicators, criteria):
                    continue
                
                # Apply volume surge filter
                if not self._check_volume_criteria(stock, criteria):
                    continue
                
                filtered_stocks.append(stock)
                
            except Exception as e:
                logger.error(f"Error filtering stock {stock.symbol}: {e}")
                continue
        
        return filtered_stocks
    
    def _check_rsi_criteria(self, indicators: Dict, criteria: StockScreeningCriteria) -> bool:
        """Check RSI criteria."""
        if criteria.rsi_min is None and criteria.rsi_max is None:
            return True
        
        rsi_values = indicators.get('rsi', [])
        if not rsi_values:
            return True
        
        current_rsi = rsi_values[-1] if isinstance(rsi_values, list) else rsi_values
        
        if criteria.rsi_min is not None and current_rsi < criteria.rsi_min:
            return False
        
        if criteria.rsi_max is not None and current_rsi > criteria.rsi_max:
            return False
        
        return True
    
    def _check_macd_criteria(self, indicators: Dict, criteria: StockScreeningCriteria) -> bool:
        """Check MACD criteria."""
        # For now, just return True - can be extended with specific MACD criteria
        return True
    
    def _check_bollinger_criteria(self, indicators: Dict, criteria: StockScreeningCriteria) -> bool:
        """Check Bollinger Bands criteria."""
        # For now, just return True - can be extended with specific Bollinger criteria
        return True
    
    def _check_volume_criteria(self, stock: Stock, criteria: StockScreeningCriteria) -> bool:
        """Check volume surge criteria."""
        if not criteria.volume_surge_threshold:
            return True
        
        if not stock.volume or not stock.avg_volume:
            return True
        
        volume_ratio = stock.volume / stock.avg_volume
        return volume_ratio >= criteria.volume_surge_threshold


def screen_momentum_stocks(db: Session, min_price_change: float = 5.0) -> List[Stock]:
    """
    Screen for momentum stocks with significant price movement.
    
    Args:
        db: Database session
        min_price_change: Minimum price change percentage
        
    Returns:
        List of momentum stocks
    """
    return db.query(Stock).filter(
        and_(
            Stock.is_active == True,
            Stock.price_change_percent >= min_price_change,
            Stock.volume > Stock.avg_volume * 1.5  # Above average volume
        )
    ).order_by(Stock.price_change_percent.desc()).limit(50).all()


def screen_oversold_stocks(db: Session, rsi_threshold: float = 30.0) -> List[Stock]:
    """
    Screen for potentially oversold stocks based on RSI.
    
    Args:
        db: Database session
        rsi_threshold: RSI threshold for oversold condition
        
    Returns:
        List of potentially oversold stocks
    """
    return db.query(Stock).filter(
        and_(
            Stock.is_active == True,
            func.json_extract_path_text(Stock.technical_indicators, 'indicators', 'rsi', '-1').cast(float) <= rsi_threshold
        )
    ).limit(50).all()


def screen_breakout_stocks(db: Session, volume_threshold: float = 2.0) -> List[Stock]:
    """
    Screen for potential breakout stocks based on volume surge.
    
    Args:
        db: Database session
        volume_threshold: Volume surge threshold (multiple of average volume)
        
    Returns:
        List of potential breakout stocks
    """
    return db.query(Stock).filter(
        and_(
            Stock.is_active == True,
            Stock.volume > Stock.avg_volume * volume_threshold,
            Stock.price_change_percent > 0  # Positive price movement
        )
    ).order_by(Stock.volume.desc()).limit(50).all()
