# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Redis for caching
redis==5.0.1
hiredis==2.2.3

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP requests for external APIs
httpx==0.25.2
aiohttp==3.9.1

# Background tasks and scheduling
apscheduler==3.10.4
celery==5.3.4

# Data processing and analysis
pandas==2.1.3
numpy==1.25.2
ta-lib==0.4.28
yfinance==0.2.28

# WebSocket support
websockets==12.0

# Email notifications
fastapi-mail==1.4.1

# Environment variables
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Type hints
mypy==1.7.1

# CORS middleware
fastapi-cors==0.0.6

# Validation
pydantic==2.5.0
email-validator==2.1.0

# Date and time utilities
python-dateutil==2.8.2

# JSON Web Tokens
PyJWT==2.8.0

# Async database support
asyncpg==0.29.0
databases[postgresql]==0.8.0

# Rate limiting
slowapi==0.1.9

# Health checks
fastapi-health==0.4.0

# API documentation
fastapi-users==12.1.2
